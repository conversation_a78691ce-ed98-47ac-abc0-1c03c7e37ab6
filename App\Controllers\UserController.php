<?php

namespace App\Controllers;

use App\Config\Config;
use App\Services\ClashRoyaleApi;
use App\Models\{User, Product};
use App\Views\View;

class UserController
{
    private $User;
    private $connectionDB;

    public function __construct(\PDO $connectionDB, int $id)
    {
        $this->connectionDB = $connectionDB;
        $this->User = new User($connectionDB, $id);
    }

    public function isSessionActive(): bool
    {
        return ($this->User->Session && $this->User->Session['Session'] == 'Activa') ? true : false;
    }

    public function setTag(string $Tag)
    {
        if (is_null($this->User->Tag))
            throw new \Exception('El usuario ya tiene un Tag asignado.');

        if ($this->User::validateTag($Tag))
            throw new \Exception('El Tag "' . $Tag . '" no es válido.');

        $resultUpdate = $this->User->update(['Tag' => $Tag]);
        if ($resultUpdate) {
            $_SESSION['Tag'] = $Tag;
            $this->User->Tag = $Tag;
            return ['state' => 'success', 'Tag' => $this->User->Tag, 'alerts' => ['<span class="cs-color-VibrantTurquoise text-center">Tag actualizado correctamente.</span>']];
        } else {
            throw new \Exception('No se pudo asignar el Tag al usuario.');
        }
    }

    public static function setUserSession($type, $usarr)
    {
        return User::setUserSession($type, $usarr);
    }

    /*     public function session(&$res) //envia datos de sessiones para java script
    {
        $res['id'] = $_SESSION['id'];
        $dateBanHideAct = AboutUsController::CSVersion(); //establecimiento de obtetos gratis
        $res['dateBanHideAct'] = \DateTime::createFromFormat('d-m-Y', $dateBanHideAct['date'])->format('d-m-Y');
    } */

    public function logout() //logout
    {
        return $this->User->logout();
    }

    public function clientError(&$res) //registrar un error en el lado del cliente
    {
        file_put_contents(PATH_ROOT . '/App/Logs/errorclient.txt', gmdate('Y-m-d H:i:s') . ' >> ' . $_POST['error'] . PHP_EOL, FILE_APPEND);
        $res['log'] = 'Error guardado correctamente';
    }

    public static function googleAccess($connectionDB) //acede con cuenta de google
    {
        return User::googleAccess($connectionDB);
    }

    public function createAcount($connectionDB, $typeAcount, $nombre, $correo, $Contraseña, $RepContraseña)
    {
        return $this->User->createAcount($connectionDB, $typeAcount, $nombre, $correo, $Contraseña, $RepContraseña);
    }

    /*  public function getNoty(&$res) //obtener las notificaciones del usuario
    {
        $connection = bdconnection();
        $getNoty = mysqli_prepare($connection, "SELECT * FROM notifications WHERE id_UsNo = ? AND Visto = 0 ORDER BY Fecha DESC");
        mysqli_stmt_bind_param($getNoty, 'i', $_SESSION['id']);
        mysqli_stmt_execute($getNoty);
        $result = mysqli_stmt_get_result($getNoty);
        $resnotyrow = mysqli_num_rows($result);
        mysqli_stmt_close($getNoty);

        ob_start();
        if ($resnotyrow > 0) {
            while ($resnoty = mysqli_fetch_assoc($result)) { ?>
                <div class="div_noty" data-visto="<?php echo $resnoty['Visto']; ?>" data-idnot="<?php echo $resnoty['id_Not']; ?>">
                    <p class="cs-color-DeepBlue"><?php echo $resnoty['Title'] ?></p>
                    <div class="div_noty_img_p">
                        <img class="w-25" src="<?php echo $resnoty['Icon'] ?>" alt="img_noty">
                        <p class="cs-color-GoldenYellow"><?php echo $resnoty['Body'] ?></p>
                    </div>
                    <div class="w-100">
                        <button id="btn_noty_elim">Eliminar</button>
                        <button id="btn_noty_visto">Visto</button>
                    </div>
                </div><br><br>
            <?php      }
        } else { ?>
            <br>
            <div class="div_noty" data-visto="0">
                <p class="cs-color-DeepBlue">Sin Notificaciones</p>
                <div class="div_noty_img_p">
                    <img src="./static/media/styles/icons/info-circle.svg" alt="img_noty">
                    <p class="cs-color-GoldenYellow">No tienes Notificaciones</p>
                </div>
                <div class="w-100">
                    <button>Eliminar</button>
                    <button>Visto</button>
                </div>
            </div>
<?php   }
        $res['res'] = ob_get_clean();
    } */

    /*     public function infjugador(&$res) //api clash royale
        {
            if ($response && !isset($json['reason'])) { //$response devolvera false si la solisitud falla, $json obtendra reason si la consultaa falla en el servodor de supercell
                $jsonString = json_encode($api['players']);
                $jsonString2 = json_encode($api['upcomingchests']);
                $data = json_decode($jsonString, true);
                $data2 = json_decode($jsonString2, true);
                ob_start();
                ?>
                <h3>Información De Jugador</h3>
                <?php imprimirInfJug($data); ?>
                <br><br>

                <h3>Proximos Cofres</h3>
                <?php imprimirCofres($data2); ?>
                <br><br>

                <?php
                $json = json_decode($response, true);
                $res['json'] = $json;
                $res['html'] = ob_get_clean();
            } else {
                $res['json'] = $json;
                $res['error'] = true;
                isset($json['message']) ? $message = $json['message'] : $message = '';
                $res['html'] = '<p class="cs-color-IntenseOrange text-center">' . curl_error($ch) . ' ' . $message . '</p>';
            }
        } */

    /* public function imprimirInfJug($data)
     {
         $properties = array();
         $arrays = array();

         foreach ($data as $key => $value) {
             if (is_array($value)) {
                 $arrays[$key] = $value;
             } else {
                 $properties[$key] = $value;
             }
         }

         echo '<table class="tb_infjug">';
         foreach ($properties as $clave => $valor) {
             switch ($clave) {
                 case 'expLevel':
                 case 'expPoints':
                 case 'totalExpPoints':
                     $img = '<img class="img_infjug" src="./static/media/styles/icons/player_stat/Experience Icon.webp" alt="Experience Icon">';
                     break;
                 case 'wins':
                     $img = '<img class="img_infjug" src="./static/media/styles/icons/player_stat/Blue Crown 1.webp" alt="Blue Crown 1">';
                     break;
                 case 'losses':
                     $img = '<img class="img_infjug" src="./static/media/styles/icons/player_stat/Red Crown 1.webp" alt="Red Crown 1">';
                     break;
                 case 'threeCrownWins':
                     $img = '<img class="img_infjug" style="width: 2.5em; height: 2.5em; margin-left: -0.5em" src="./static/media/styles/icons/player_stat/Blue Crown Animated.gif" alt="Blue Crown Animated"><img class="img_infjug" style="width: 2.5em; height: 2.5em; margin-left: -0.5em" src="./static/media/styles/icons/player_stat/Blue Crown Animated.gif" alt="Blue Crown Animated"><img class="img_infjug" style="width: 2.5em; height: 2.5em; margin-left: -0.5em" src="./static/media/styles/icons/player_stat/Blue Crown Animated.gif" alt="Blue Crown Animated">';
                     break;
                 case 'challengeMaxWins':
                 case 'tournamentBattleCount':
                     $img = '<img class="img_infjug" style="width: 2.5em; height: 2.5em;" src="./static/media/styles/icons/player_stat/icon_menu_tournament.webp" alt="icon_menu_tournament">';
                     break;
                 case 'warDayWins':
                     $img = '<img class="img_infjug" src="./static/media/styles/icons/player_stat/icon_menu_clan_wars.webp" alt="icon_menu_clan_wars">';
                     break;
                 case 'starPoints':
                     $img = '<img class="img_infjug" style="width: 1.5em; height: 1.5em;"  src="./static/media/styles/icons/player_stat/icon_star_level.webp" alt="icon_star_level">';
                     break;
                 default:
                     $img = null;
             }

             echo "<tr>
       <td>
       {$img}<p class='cs-color-DeepBlue'>{$clave}: </p>&nbsp;<p class='cs-color-DeepBlue'>&nbsp;{$valor}</p>
       </td>
       </tr>";
         }
         echo '</table>';

         foreach ($arrays as $clave => $valor) {

             if (
                 $clave == 'badges' ||
                 $clave == 'achievements' ||
                 $clave == 'cards' ||
                 $clave == 'supportCards' ||
                 $clave == 'currentDeck' ||
                 $clave == 'currentFavouriteCard' ||
                 $clave == 'leagueStatistics' ||
                 $clave == 'currentDeckSupportCards' ||
                 $clave == 'currentPathOfLegendSeasonResult' ||
                 $clave == 'lastPathOfLegendSeasonResult' ||
                 $clave == 'bestPathOfLegendSeasonResult'
             ) {
                 continue;
             } else {
                 echo "<h3>{$clave}</h3>";
                 imprimirInfJug($valor);
             }
         }
     }

     public function imprimirCofres($dat)
     {
         $data = $dat['items'];
         foreach ($data as $clave) {
             $index = $clave['index'];
             $date = $clave['name'];
             echo "<div class='div_cofres'>";
             echo "<img class='img_cofres' src='./static/media/styles/user/chests/{$date}.webp' alt='{$date}'>";
             echo "<span class='span_index_cofres cs-color-DeepBlue'>+{$index}</span>";
             echo "</div>";
         }
     }

     public function Notificacion($type, $id_usu, $option = null) //insertar nuevas notificaciones
     {
         $arrayRes = ['state' => 'inprogres', 'msg' => []];
         $connection = bdconnection();
         switch ($type) {
             case 'ins-not': //inserta nuevas notificaciones
                 $insNoty = mysqli_prepare($connection, "INSERT INTO notifications(id_UsNo, Title, Body, Icon, Fecha) VALUES(?, ?, ?, ?, UTC_TIMESTAMP())");
                 mysqli_stmt_bind_param($insNoty, 'isss', $id_usu, $option['Title'], $option['Body'], $option['Icon']);
                 mysqli_stmt_execute($insNoty);
                 mysqli_stmt_close($insNoty);

                 if ($insNoty) {
                     $arrayRes['state'] = 'succes';
                 } else {
                     $arrayRes['state'] = 'error';
                     array_push($arrayRes['msg'], '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error</span>');
                 }
                 break;
             case 'vis-not': //pone en visto las notificaciones
                 $insNoty = mysqli_prepare($connection, "UPDATE norificiones SET Visto = 1 WHERE id_Not = ? AND id_UsNo = ?");
                 mysqli_stmt_bind_param($insNoty, 'ii', $option['id_Not'], $id_usu);
                 mysqli_stmt_execute($insNoty);
                 mysqli_stmt_close($insNoty);
                 break;
         }
         return $arrayRes;
     }*/

    public function createCSAcount()
    {
        throw new \Exception("Ya no se permite crear cuantas con formulario, por favor registrese con google");
        /*         $resCreateacount = $this->createAcount('sistem', $_POST['Nombre'], null, $_POST['Contraseña'], $_POST['RepContraseña']);
                if ($resCreateacount['state'] == 'success') {
                    $this->insertSession('sistem', $resCreateacount['usu'], $res);
                } else {
                    $res['res'] = $resCreateacount['alerts'][0];
                    $res['login'] = false;
                } */
    }

    public static function showBasicData()
    {
        $View = new View('userDataView');
        return $View->render();
    }

    /*     public function loginCSAcount()
    {
        if ($_POST['Contraseña'] == 'google') {
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">La Contraseña google no esta permitida</span>';
            return;
        }
        $connection = bdconnection();
        $result = mysqli_prepare($connection, "SELECT * FROM users WHERE Nombre = ? OR Usuario = ?");
        mysqli_stmt_bind_param($result, "ss", $_POST['Nombre'], $_POST['Nombre']);
        mysqli_stmt_execute($result);
        $resultfin = mysqli_stmt_get_result($result);
        $us = mysqli_fetch_array($resultfin);
        $rows = mysqli_num_rows($resultfin);
        mysqli_stmt_close($result);

        if ($rows > 0) {
            if ($rows == 1 && ($us['Nombre'] == $_POST['Nombre'] || $us['Usuario'] == $_POST['Nombre']) && $us['Estado'] == 'Activo' && (password_verify($_POST['Contraseña'], $us['Contraseña']) || $_POST['Contraseña'] == $us['Contraseña'])) {
                $versessionabierta = mysqli_query($connection, "SELECT * FROM sessions WHERE id_UsSe = '" . $us['id_Usu'] . "' AND Session = 'Activa'");
                $rowses = mysqli_num_rows($versessionabierta);
                $sesion = mysqli_fetch_assoc($versessionabierta);
                if ($rowses == 1) { //verificasion si hay una session activa
                    $loguotsession = mysqli_query($connection, "UPDATE sessions SET Session = 'Eliminada', Fec_sal = UTC_TIMESTAMP() WHERE id_UsSe = '" . $sesion['id_UsSe'] . "'");
                    if ($loguotsession) {
                        insertSession('sistem', $us, $res);
                        $res['res'] =  '<span class="alert-info">Iniciando Sesión...</span>';
                    } else {
                        $res['res'] =  '<span class="cs-color-IntenseOrange text-center">ha ocurrido un error, intentelo de  nuevo</span>';
                    }
                } else { //iniciar nueva session
                    $res['res'] =  '<span class="alert-info">Iniciando Sesión...</span>';
                    insertSession('sistem', $us, $res);
                }
            } else { //la contra es incorrecta o hay multiples cuentas
                $rows > 1 ? $res['res'] = "<span class='cs-color-IntenseOrange text-center'>Hay múltiples cuentas con el mismo Nombre. Por favor, proporciona tu Usuario unico para iniciar sesión.</span>" : $res['res'] = '<span class="cs-color-IntenseOrange text-center">Nombre, Usuario o Contraseña incorrecta</span>';
            }
        } else {
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">Nombre, Usuario o Contraseña incorrecta</span>';
        }
    } */

    /*     public function resgisterInf()
    {
        $connection = bdconnection();
        $informacion = htmlspecialchars($_POST['informacion'], ENT_QUOTES, 'UTF-8');
        $inf = nl2br($informacion);

        $inf_sin_br = str_replace("<br>", "", $inf);
        $inf_limpio = strip_tags($inf_sin_br);
        $num_ver = true;

        if (strlen($inf_limpio) > 150) {
            $num_ver = false;
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">La cantidad maxima de caracteres son 150, caracteres insertados: ' . strlen($inf_limpio) . '</span>';
        }

        if ($num_ver) {
            $queryme = mysqli_prepare($connection, "UPDATE users SET Informacion = ? WHERE id_Usu = ?");
            mysqli_stmt_bind_param($queryme, "si", $inf, $_SESSION['id']);
            mysqli_stmt_execute($queryme);
            mysqli_stmt_close($queryme);

            if ($queryme) {
                $res['success'] =  true;
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se Guardo Correctamente</span>';
                $res['inf'] = $inf;
            } else {
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Ha ocurrido un error Interno</span>';
            }
        }
    }

    public function camBan()
    {
        $connection = bdconnection();
        if ($_POST['banner'] != null) {
            $querydat = mysqli_prepare($connection, "UPDATE users SET Banner = ? WHERE Usuario = ?");
            mysqli_stmt_bind_param($querydat, "ss", $_POST['banner'], $_SESSION['Usuario']);
            mysqli_stmt_execute($querydat);
            mysqli_stmt_close($querydat);

            if ($querydat) {
                $_SESSION['Banner'] = $_POST['banner'];
                $res['Banner'] = $_SESSION['Banner'];
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se cambio el banner correctamente</span>';
            } else {
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error</span>';
            }
        } else {
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">Seleccione un banner</span>';
        }
    }

    public function camdatUsu()
    {
        $connection = bdconnection();
        $correo = $_POST['Correo'];
        $fechanac = $_POST['FechaNac'];
        $genero = $_POST['Genero'];
        $tag = $_POST['Tag'];
        $ver_email = true;

        if (!filter_var($correo, FILTER_VALIDATE_EMAIL)) {
            $ver_email = false;
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">El correo no es valido</span>';
        }

        if ($ver_email) {
            $querydat = mysqli_prepare($connection, "UPDATE users SET Correo = ?, FechaNac = ?, Genero = ?, Tag = ? WHERE id_Usu = ?");
            mysqli_stmt_bind_param($querydat, "ssssi", $correo, $fechanac, $genero, $tag, $_SESSION['id']);
            mysqli_stmt_execute($querydat);
            mysqli_stmt_close($querydat);

            if ($querydat) {
                $_SESSION['Correo'] = $correo;
                $_SESSION['FechaNac'] = $fechanac;
                $_SESSION['Genero'] = $genero;
                $_SESSION['Tag'] = $tag;
                $res['success'] =  true;
                $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Los Datos Se actualizaron Correctamente</span>';
            } else {
                $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error, verifique los datos e intentelo de nuevo</span>';
            }
        }
    }

    public function camAba()
    {
        $connection = bdconnection();
        $file = $_FILES['webp_ava'];
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = substr(str_shuffle($characters), 0, 12);
        $namefot = $randomString;
        $num_archivos = $file != null ? count($_FILES['webp_ava']['name']) : 0;

        if ($num_archivos == 1) {
            $rfoto = $file['tmp_name'][0];
            $archivo = $file['name'][0];
            $extension = strtolower(pathinfo($archivo, PATHINFO_EXTENSION));
            if (is_uploaded_file($rfoto)) {
                if ($extension == 'webp') {
                    $fname = $namefot . "." . $extension;
                    rename("../static/media/styles/user/avatars/" . $_SESSION['Avatar'], "../static/media/styles/user/cache/" . $_SESSION['Avatar']);
                    rename($rfoto, "../static/media/styles/user/avatars/" . $fname);

                    $camfot = mysqli_prepare($connection, "UPDATE users SET Avatar = ? where Usuario = ?");
                    mysqli_stmt_bind_param($camfot, "ss", $fname, $_SESSION["Usuario"]);
                    mysqli_stmt_execute($camfot);
                    mysqli_stmt_close($camfot);

                    if ($camfot) {
                        $_SESSION['Avatar'] = $fname;
                        $res['success'] =  true;
                        $res['avatar'] =  $_SESSION['Avatar'];
                        $res['res'] = '<span class="cs-color-VibrantTurquoise text-center">Se cambio el Avatar Exitosamente</span>';
                    } else {
                        $res['res'] = '<span class="cs-color-IntenseOrange text-center">Ha ocurrido un error</span>';
                    }
                } else {
                    $res['res'] = '<span class="cs-color-IntenseOrange text-center">El formato de la imagen no es valida, archivo: ' . $archivo . '</span>';
                }
            }
        }
    } */
}
