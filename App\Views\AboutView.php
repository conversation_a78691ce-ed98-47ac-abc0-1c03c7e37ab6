<div class="text-center">
        <div class="m-1">
                <h3 class="cs-color-GoldenYellow">Clash Strategic</h3>
                <p class=" cs-color-GoldenYellow">Contacto: <a class="cs-link cs-link--default"
                                href="mailto:<?php echo $data['contact']['email']; ?>">
                                <?php echo $data['contact']['email']; ?></a>
                </p>
                <p class="cs-color-GoldenYellow">Api Versión: <span class="cs-color-LightGrey">
                                <?php echo $data['version']; ?> <img class="cs-tooltip-image"
                                        src="./static/media/styles/icons/info-circle.svg" alt="msgInfo"
                                        data-inf="Versión del servidor que proporciona los datos a la aplicación (webapp)."></span>
                </p>
                <p class="cs-color-GoldenYellow">Api Actualizado el: <span class="cs-color-LightGrey">
                                <?php echo $data['CSDate']; ?></span></p>
                <p class="cs-color-GoldenYellow">WebApp Versión: <span class="cs-color-LightGrey" id="webapp-version">
                                <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                                        alt="msgInfo"
                                        data-inf="Versión de la aplicación web que el usuario está ejecutando.">
                        </span>
                </p>
                <p class="cs-color-GoldenYellow">WebApp Actualizado el:
                        <span class="cs-color-LightGrey" id="webapp-updated"></span>
                </p>
        </div><br>

        <hr><br>
        <div id="div_redes_sociales">
                <a href="<?php echo $data['contact']['social_media']['Youtube']; ?>" target="_blank">
                        <img class="img_red" title="Youtube" src="static/media/styles/icons/redes_sociales/youtube.webp"
                                alt="youtube.webp"></a>
                <a href="<?php echo $data['contact']['social_media']['Instagram']; ?>" target="_blank">
                        <img class="img_red" title="Instagram"
                                src="static/media/styles/icons/redes_sociales/instagram.webp" alt="instagram.webp"></a>
                <a href="<?php echo $data['contact']['social_media']['X']; ?>" target="_blank">
                        <img class="img_red" title="X" src="static/media/styles/icons/redes_sociales/x.webp"
                                alt="x.webp"></a>
                <a href="<?php echo $data['contact']['social_media']['TikTok']; ?>" target="_blank">
                        <img class="img_red" title="TikTok" src="static/media/styles/icons/redes_sociales/tik-tok.webp"
                                alt="tik-tok.webp"></a>
        </div>

        <div class="m-1">
                <p>"Este material no es oficial y no está respaldado por Supercell. Para obtener más información,
                        consulte la
                        <a class="cs-link cs-link--default" target="_blank"
                                href="https://supercell.com/en/fan-content-policy/es/">
                                Política de contenidos de fans</a>."
                </p>
        </div><br>

        <div class="m-0-25">
                <a class="cs-link cs-link--default m-1" target="_blank"
                        href="https://clashstrategic.notion.site/Versiones-de-Clash-Strategic-50f2587783d649bd97447e35a694e132?pvs=4">
                        Historial de versiones.</a>
                <a class="cs-link cs-link--default m-0-25" href='./PrivacyPolicy' target="_blank">Política de
                        Privacidad.</a>
                <a class="cs-link cs-link--default m-0-25" href='./TermsService' target="_blank">Términos de
                        servicio</a>
        </div>
</div>
<script>
        (function () {
                const swVersion = localStorage.getItem('sw_version') || 'N/A';
                $('#webapp-version').prepend(swVersion + " ");

                const dateTimeString = localStorage.getItem('sw_datetime');

                if (dateTimeString) {
                        const date = new Date(dateTimeString);
                        const formattedDate = date.toLocaleDateString('en-GB', {
                                day: '2-digit',
                                month: 'long',
                                year: 'numeric'
                        });
                        $('#webapp-updated').text(formattedDate);
                } else {
                        $('#webapp-updated').text('N/A');
                }
        })();
</script>