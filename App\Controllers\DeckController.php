<?php

namespace App\Controllers;

use App\Models\{User, Deck};
use App\Controllers\ProductController;
use tools\deckanalyzer\DeckAnalyzer;
use tools\deckbuilder\DeckBuilder;
use App\Views\{View, DeckAnalysisPresenter};

class DeckController
{
    private $connectionDB;
    private $ProductController;

    public function __construct(\PDO $connectionDB)
    {
        $this->connectionDB = $connectionDB;
        $this->ProductController = new ProductController($connectionDB);
    }

    public function analyze(array $namesCards, int $anaEvo, string $level, float $version = 1.0): mixed
    {
        $resPurchase = $this->ProductController->purchaseDeckAnalyzer($level, $version);

        if (!$resPurchase['state'] == 'success')
            throw new \Exception("Error al Hacer el Pago");

        $Deck = new Deck($namesCards, 11, $anaEvo);
        $rawDataForPresenter = [
            'averages' => [],
            'data' => [],
            'type' => $level,
            'defenseTypeNames' => null,
            'defenseTypeDescriptions' => null
        ];
        $alerts = [];

        $thresholdsPath = ($level == 'basic')
            ? PATH_ROOT . 'App/Data/tools/DeckAnalyzer/thresholds_basic.json'
            : PATH_ROOT . 'App/Data/tools/DeckAnalyzer/thresholds.json';
        $thresholdsData = json_decode(file_get_contents($thresholdsPath), true);

        $defenseJsonPath = PATH_ROOT . "App/Data/tools/DeckAnalyzer/defenseCoverage.json";
        $defenseJson = file_exists($defenseJsonPath) ? json_decode(file_get_contents($defenseJsonPath)) : (object) ['names' => new \stdClass(), 'descriptions' => new \stdClass()];

        $statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/constants.json'));
        $pathsStrategies = [
            "General" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/General.json',
            "Defensa" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Defense.json',
            "Adaptavilidad" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Adaptability.json',
            "Agresivo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Aggressive.json',
            "Bait" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Bait.json',
            "Ciclo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Cycle.json',
            "Precion" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Pressure.json',
            "Push" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Push.json',
            "SplitPush" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/SplitPush.json'
        ];

        $DeckAnalyzer = new DeckAnalyzer($statCards, $constants, $pathsStrategies, $Deck, $level);

        // Common analysis for all levels
        $rawDataForPresenter['data'] = [
            'shortCycle' => $DeckAnalyzer->getShortCycle(),
            'averageElixirCost' => $DeckAnalyzer->getAverageElixirCost(),
            'archetype' => $DeckAnalyzer->identifyArchetype()
        ];
        $rawDataForPresenter['averages']['Ataque'] = $DeckAnalyzer->getBattleRolesScores('attack');
        $rawDataForPresenter['averages']['Defensa'] = $DeckAnalyzer->getBattleRolesScores('defense');

        // Analysis specific to Intermediate and Advanced levels
        if ($level === 'intermediate' || $level === 'advanced') {
            $rawDataForPresenter['weaknesses'] = $DeckAnalyzer->getDeckWeaknesses();
            $rawDataForPresenter['defenseCoverage'] = $DeckAnalyzer->getDefenseTypesCoverage();
            $alerts[] = '<span class="cs-color-VibrantTurquoise text-center">Mazo Analizado</span>';
        }

        // Analysis specific to Advanced level
        if ($level === 'advanced') {
            $rawDataForPresenter['averages']['Versatilidad'] = $DeckAnalyzer->getScoreVersatility();
            $rawDataForPresenter['averages']['Sinergia'] = $DeckAnalyzer->getScoreSynergy();
            $rawDataForPresenter['averages']['Versatilidad']['averagesPerGroup'] = $DeckAnalyzer->getBattleRolesScoresByGroup($rawDataForPresenter['averages']['Versatilidad']);
        }

        $viewData = [];
        $viewOutput = '';

        $rawDataForPresenter['defenseTypeNames'] = $rawDataForPresenter['defenseTypeNames'] ?? $defenseJson->names;
        $rawDataForPresenter['defenseTypeDescriptions'] = $rawDataForPresenter['defenseTypeDescriptions'] ?? $defenseJson->descriptions;

        $presenter = new DeckAnalysisPresenter($rawDataForPresenter, $thresholdsData, $defenseJson, $level);
        $viewData = $presenter->getFormattedDataForView();
        if (empty($viewData) && isset($presenter)) {
            $viewData = $presenter->getFormattedDataForView();
        } elseif (empty($viewData)) {
            $viewData = $rawDataForPresenter;
        }

        $View = new View('DeckAnalysisView', $viewData);
        $viewOutput = $View->render();

        return match ($level) {
            'advanced' => array_merge($resPurchase, ["view" => $viewOutput, "alerts" => $alerts]),
            'intermediate' => ["view" => $viewOutput, "alerts" => $alerts],
            'basic' => $viewOutput
        };
    }

    public function create(): array
    {
        $res = ["state" => "inprogress", "alerts" => [], 'res' => ''];

        if ($_POST['level'] == "advanced" && !is_float($_POST['MazOpt']) && $_POST['MazOpt'] > 3) {
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">Las opciones de mazos no pueden ser mayor a 3</span>';
            return $res;
        }

        $payDeckBuilder = $this->ProductController->purchaseDeckBuilder($_POST['level'], $_POST['version']);

        if ($payDeckBuilder['state'] == 'success') {
            $statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
            $constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/constants.json'));
            $DeckBuilder = new DeckBuilder($statCards, $constants, $_POST['level']);
            $DeckCreated = $DeckBuilder->create(
                $_POST['winConditionName'],
                $_POST['attackLevel'],
                // Options Intermediate
                $_POST['ChampeonName'] ?? 'Libre',
                $_POST['Estrategias'] ?? 'Control',
                $_POST['PrElixir'] ?? '3.0',
                // Options Advanced
                $_POST['iteration'] ?? 1,
                $_POST['MazOpt'] ?? 1,
                $_POST['evo'] ?? 0
            );
            $res = array_merge($res, $DeckCreated);
            $res['state'] = 'success';
            $res['Gems'] = $_SESSION['Gems'];
        } else {
            $res['res'] = $payDeckBuilder['res'];
            array_merge($res['alerts'], $payDeckBuilder['alerts']);
        }
        return $res;
    }

    public function guardarMazo(array $DeckNames, int $nmazo, &$res)
    {
        $User = new User($this->connectionDB, $_SESSION['id']);
        $DeckNames = json_decode($_POST['mazo'], true);
        $User->saveDeck($nmazo, $DeckNames, $res);
        return $res;
    }
}
