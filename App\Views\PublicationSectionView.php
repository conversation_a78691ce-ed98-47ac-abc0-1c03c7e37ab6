<div class="container">
    <!--##################################### Encuesta y Publiquero Toggles ######################################-->
    <div id="div_enc_pub" class="div_toggle">
        <div class="div_barrasup_perfil">
            <h3>Publicar</h3>
            <button id="btn_x-not" class="btn_x_perfil">X</button>
        </div><br><br><br>
        <!--##################################### Publiquero ######################################-->
        <button id="btn_publicacion"><img class="img_ban_pub" src="./static/media/styles/banner_pub.webp"
                alt="banner_encuesta"></button><br>
        <div id="div_publiquero" style="display: none;"><br>
            <p>Publicación permite compartir tácticas y estrategias para mejorar en el juego.</p>
            <form id="frm_publicar" enctype="multipart/form-data" autocomplete="off">
                <textarea class="text_publi" id="text_pub" name="publicacion"
                    placeholder="Comparte tus tácticas para mejorar juntos."></textarea><br>
                <p>Subir una captura que ilustre tu publicación</p>
                <input type="file" name="files_pub[]" id="file-1" accept="image/*" multiple><br>
                <div id="div_show_img_pub"></div>
                <div id="alertp1"></div>
                <button class="cs-btn cs-btn--medium cs-btn--primary cs-bg-color-1" type="submit" id="publicar"
                    name="publicar">Publicar</button></br>
            </form>
        </div><br>
        <!--##################################### Encuesta ######################################-->
        <button id="btn_encuesta"><img class="img_ban_enc" src="./static/media/styles/banner_enc.webp"
                alt="banner_encuesta"></button><br>
        <div id="div_encuesta" style="display: none;">
            <p>Encuesta permite crear y participar en encuestas relacionadas con Clash Royale.</p>
            <form id="frm_enc" data-ajax-submit="default" data-ajax-success="env-enc">
                <input id="pregunta_enc" type="cs-color-DeepBlue" name="Pregunta" placeholder="Pregunta" required
                    autocomplete="off"><br>
                <input id="input_option1" type="cs-color-DeepBlue" name="Opcion1" placeholder="Opcion#1" required
                    autocomplete="off"><br>
                <input id="input_option2" type="cs-color-DeepBlue" name="Opcion2" placeholder="Opcion#2" required
                    autocomplete="off"><br>
                <input id="input_option3" type="cs-color-DeepBlue" name="Opcion3"
                    placeholder="Opcion#3 (No obligatorio)" autocomplete="off"><br><br>
                <label for="">Fecha de Expiración:</label>
                <input type="datetime-local" name="fecExp" min="" max="" required><br>
                <span class="cs-color-DeepBlue" style="font-size: var(--text-font-size-p2);">min: 24hrs max:
                    7dias</span><br>
                <div id="span_alert_enc"></div><br>
                <button type="submit" onclick="setMinMaxEnc()" name="submit_frmencuesa"
                    class="cs-btn cs-btn--medium cs-btn--primary cs-bg-color-1">Publicar</button>
            </form>
        </div><br>
        <!--##################################### Clan ######################################-->
        <!-- <button id="btn_com_clan">
                <h1>Clan</h1>
            </button><br>
            <div id="div_com_clan" style="display: none;">
                <p>Clan permite Compatir tu clan con la comunidad de manera personalizada e incentivadora.</p>
                <form id="frm_clan" data-ajax-submit="default" data-ajax-success="pub-clan">
                    <input type="url" name="link_clan" placeholder="link del clan para ver opciones">
                    <div id="div_alert"></div><br>
                    <button type="submit" name="submit_frmclan" class="cs-btn cs-btn--medium cs-btn--primary cs-bg-color-1">Publicar</button>
                </form>
            </div><br> -->
        <p class="text-center cs-color-DeepBlue">Se añadiran mas funciones de interaccion con la comunidad en proximas
            actualizaciones.</p>
    </div>

    <main class="main">
        <section class="section">
            <!--##################################### Banner ######################################-->
            <div id="div_banner_perfil">
                <div class="div_perfil"> <!-- Baner perfil -->
                    <div id="btn_menu_perfil" class="perfil" data-idpubusu="<?php

                    use App\Controllers\AboutUsController;

                    echo $_SESSION['id'] ?>">
                        <div class="banner_perfil">
                            <img id="img_banner_per_usu"
                                src="./static/media/styles/user/banners/<?php echo $_SESSION['Banner']; ?>"
                                class="img_banner" alt="<?php echo $_SESSION['Banner']; ?>">
                            <img class="avatar_perfil"
                                src="./static/media/styles/user/avatars/<?php echo $_SESSION['Avatar'] ?>"
                                alt="<?php echo $_SESSION['Avatar'] ?>">
                        </div>
                        <p class="cs-color-DeepBlue p_nom_perfil"><?php echo $_SESSION['Nombre'];
                        if ($_SESSION['Verificado'] == true) {
                            echo '<img id="img_logo_verificado" src="./static/media/styles/icons/icon_verificado.webp" alt="verificado">';
                        } ?></p>
                    </div>
                </div>

                <div id="div_inf_usu">
                    <div id="div_img_alert_noty" style="display: none;">
                        <img id="icon cs-icon--small" src="./static/media/styles/icons/menu_opc/icon_noty.webp"
                            alt="icon noty">
                    </div>
                    <div id="div_opciones">
                        <div id="div_menu"> <!-- boton opciones -->
                            <button id="btn_menu_enc_pub" class="btn_menu"><img class="img_logo_menu"
                                    src="./static/media/styles/icons/icon_pub.webp" alt="logo_menu_enc_pub"></button>
                            <button id="btn_menu_opc" class="cs-btn cs-btn--medium cs-btn--primary"><img
                                    class="img_logo_menu" src="./static/media/styles/icons/menu/logo_menu.svg"
                                    alt="logo_menu"></button>
                        </div>
                        <div id="div_menu_contenido">
                            <div id="menu_opciones"><!-- div_sections_content de opciones -->
                                <?php if ($_SESSION['TypeAcount'] == 'invitado') { ?>
                                    <div id="btn_bienvenido" class="a_conten"
                                        onclick="showDivToggle('showToggle'); api({PreCS: true}, 'get-rl', null, $('#div_tog_gen_con'));">
                                        <img class="cs-icon cs-icon--medium" src="./static/media/styles/logo/logo_cs.webp"
                                            alt="¡Bienvenido!">
                                        <p>¡Bienvenido!</p>
                                    </div><br>
                                    <div class="a_conten"
                                        onclick="showDivToggle('showToggle'); api({RegLog: true, type: 'reg'}, 'get-rl', null, null);">
                                        <img class="cs-icon cs-icon--medium"
                                            src="./static/media/styles/icons/menu_opc/icon_registro.webp" alt="crear">
                                        <p>Crear Cuenta</p>
                                    </div><br>
                                    <div class="a_conten"
                                        onclick="showDivToggle('showToggle'); api({RegLog: true, type: 'log'}, 'get-rl', null, null);">
                                        <img class="cs-icon cs-icon--medium"
                                            src="./static/media/styles/icons/menu_opc/icon_login.webp" alt="login">
                                        <p>Iniciar Sesion</p>
                                    </div><br>
                                <?php }
                                if ($_SESSION['TypeAcount'] != 'invitado') { ?>
                                    <div id="span_noti" class="a_conten">
                                        <img class="cs-icon cs-icon--medium"
                                            src="./static/media/styles/icons/menu_opc/icon_noty.webp" alt="logo_not">
                                        <p>Notificaciones</p>
                                    </div>
                                <?php } ?>
                                <!-- <a id="a_metodo_cs" class="a_conten" href="https://clashstrategic.notion.site/M-todo-Clash-Strategic-e0da2ec4cdda46cbb08ca3c6d7e81298?pvs=4" target="_blank">
                                <img class=" icon cs-icon--medium" src="./static/media/styles/logo/logo_cs.webp" alt="logo_cs">
                                <p>Método CS</p>
                            </a><br> -->
                                <!-- <div class="a_conten" href="https://drive.google.com/file/d/1cy2pugRiCOfR3Lj1CXm9Ut1B1oSZw7RQ/view?usp=sharing" target="_blank"><img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/menu_opc/icon_desc.webp" alt="logo_descargar_app"><p>Descargar App</p></div><br> -->
                                <hr>
                                <!-- <div class="a_conten" onclick="location.href='./sobrenosotros'" target="_blank">
                                <img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/info-circle.svg" alt="logo_not">
                                <p>Sobre Nosotros</p>
                            </div><br> -->
                                <div id="span_acercade" class="a_conten">
                                    <img class="cs-icon cs-icon--medium"
                                        src="./static/media/styles/icons/info-circle.svg" alt="logo_not">
                                    <p>Acerca De</p>
                                </div>
                                <div id="div_get_sb" class="a_conten">
                                    <img class=" icon cs-icon--medium"
                                        src="./static/media/styles/icons/menu_opc/icon_acercade.webp"
                                        alt="logo_acercade">
                                    <p style="font-size:smaller;">Sobre Nosotros</p>
                                </div>
                                <?php if ($_SESSION['TypeAcount'] != 'invitado') { ?>
                                    <div class="a_conten"
                                        onclick="if(confirm('¿Esta seguro que desea Cerrar la Session Actual?')){api({ session: false }, 'cer-ses')}">
                                        <img class="cs-icon cs-icon--medium"
                                            src="./static/media/styles/icons/menu_opc/icon_logout.webp" alt="logo_not">
                                        <p style="font-size:smaller;">Cerrar Session</p>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

            </div><br>
            <!--##################################### Anuncios ######################################-->
            <div id="div_banner_anuncios">
                <div class="div_ban_anu"><img class="img_banners_anuncios" src="./static/media/ads/CS/banner_1.webp"
                        alt="banner_logo"></div>
                <div class="div_ban_anu"><img class="img_banners_anuncios" src="./static/media/ads/CS/banner_2.webp"
                        alt="banner_pre"></div>
                <div class="div_ban_anu"><img class="img_banners_anuncios" src="./static/media/ads/CS/banner_3.webp"
                        alt="banner_inf"></div>
                <div id="div_ban_anu_act" class="div_ban_anu">
                    <img class="img_banners_anuncios" src="./static/media/ads/CS/banner_act.webp" alt="banner_actu">
                    <div>
                        <p>Nueva version de CS "<?php echo AboutUsController::CSVersion()['inf']['content']; ?>" con
                            nuevas funciones disponibles, revisalas <button id="btn_ban_act">Aqui</button></p>
                    </div>
                </div>
                <div class="div_ban_anu">
                    <img class="img_banners_anuncios" src="./static/media/ads/CS/banner_rep_bug.webp" alt="banner_actu">
                    <div>
                        <p>Ayudanos a mejorar CS con un reporte de cualquier error o bug que estes experimentando,
                            evialos al sig correo <a class="cs-link cs-link--default" target="_blank"
                                href="mailto:<EMAIL>">Aqui</a></p>
                    </div>
                </div>
            </div><br><br><br>
        </section>
        <section class="section">
            <!--##################################### Publicaciones #################################-->
            <div id="nuevaspublicaciones"><br><br><br><br></div>
        </section><br><br><br>
    </main>
</div>