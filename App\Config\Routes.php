<?php
session_start();
define('PATH_ROOT', realpath(__DIR__ . '/../../') . '/');
require_once __DIR__ . '/Autoloader.php';

use App\Controllers\{User<PERSON>ontroller, DeckController, CardController, ProductController, SectionController, AboutUsController};
use App\Config\{Boot, Router, Config};
use App\Services\{ConnectionDBPool};
use App\Views\View;

function setSessionInvitado(&$TypeAcount)
{
    $usarr = [
        'id_Usu' => null,
        'Tag' => null,
        'Correo' => null,
        'FechaNac' => null,
        'Verificado' => null,
        'Usuario' => null,
        'Estado' => null,
        'created_at' => null,
        'TypeAcount' => null,
        'Avatar' => 'defect.webp',
        'Banner' => 'defect.webp',
        'Nombre' => 'invitado',
        'Coins' => 0,
        'Gems' => 0,
        'Mazos' => '["", "", "", "", "", "", "", "", "", ""]'
    ];
    UserController::setUserSession('invitado', $usarr);
    $TypeAcount = $_SESSION['TypeAcount'];
}

$TypeAcount = isset($_SESSION['TypeAcount']) ? $_SESSION['TypeAcount'] : null;

// Si es null, intentar obtener una conexión para insertar sesión de invitado
if ($TypeAcount == null) {
    setSessionInvitado($TypeAcount);
} else {
    $requiredCookies = ['CSVersion', 'CSDate', 'Mazos', 'TypeAcount', 'Timezone', 'byOrdenCards', 'nmazo', 'sound_effects'];
    $allCookiesRequired = true;

    foreach ($requiredCookies as $cookie) {
        if (!isset($_COOKIE[$cookie])) {
            $allCookiesRequired = false;
            break;
        }
    }

    if (!$allCookiesRequired) { // Si falta alguna cookie, iniciar una nueva sesión
        if (empty($_COOKIE)) {
            $res = ['state' => 'error', 'alerts' => ['Las cookies están deshabilitadas. Por favor, habilita las cookies para continuar.']];
            echo json_encode($res, JSON_FORCE_OBJECT);
            return;
        }

        if ($TypeAcount == 'invitado') {
            setSessionInvitado($TypeAcount);
        } else { //de momento solo se puede iniciar sesión como invitado
            setSessionInvitado($TypeAcount);
        }
    }
}

$router = new Router();

$router->addRoute('GET', '/api/home', [Boot::class, 'showHome']);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $requesUser = requesUser($TypeAcount);
    if ($requesUser['state'] == 'success') {
        $router->addRoute('POST', '/api/App/Config/Routes.php', [$requesUser['class'], $requesUser['method']], $requesUser['params']);
    } else {
        echo json_encode($requesUser);
        exit();
    }
}

function requesUser($TypeAcount)
{
    $class = "";
    $method = "";
    $params = ["class" => [], "method" => []];
    $actionExecuted = false;

    $res = ['state' => 'inprogres', 'res' => '', 'alerts' => [], 'TypeAcount' => $TypeAcount];

    try {
        // Funciones que necesitan conexión a la base de datos
        $dbRequiredActions = ['session', 'credential', 'analizarMazo', 'guardarMazo', 'crearMazo', 'showUserBasicData', 'setTag', 'LemonSqueezy', 'getSectionMembers'];
        $connectionDB = null;

        // Verificar si la solicitud actual necesita una conexión
        $needsDBConnection = !empty(array_intersect($dbRequiredActions, array_keys($_POST)));

        if ($needsDBConnection) {
            $DatabasePool = new ConnectionDBPool(Config::getParamDB($_SERVER['SERVER_NAME']));
            $connectionDB = $DatabasePool->getConnection();
            if ($connectionDB === null) {
                $res = ['state' => 'error', 'res' => '', 'alerts' => ['Lo sentimos, no podemos conectarnos a la base de datos en este momento. Por favor, inténtalo de nuevo más tarde. ¡Gracias por tu paciencia!']];
                echo json_encode($res);
                return;
            }
        }

        // Verificar la sesión solo si es necesario
        if ($TypeAcount == 'google' && $needsDBConnection) {
            $userSession = new UserController($connectionDB, $_SESSION['id']);
            if (!$userSession->isSessionActive()) {
                $userSession->logout();
                $_SERVER['SERVER_NAME'] == 'localhost' ? header('Location: /clash-strategic-api/home') : header('Location: /home');
                return; // Importante: Agrega un return para evitar ejecutar el resto del código
            }
        }

        if ($TypeAcount == 'google' || $TypeAcount == 'sistem' || $TypeAcount == 'invitado') {
            if ($needsDBConnection) {
                if (isset($_POST['session'])) {
                    $class = UserController::class;
                    $method = 'logout';
                    $params['class'] = [$connectionDB, $_SESSION['id']];
                    $params['method'] = [$res];
                    $actionExecuted = true;
                }

                if (isset($_POST['credential'])) {
                    $class = UserController::class;
                    $method = 'googleAccess';
                    $params['method'] = [$connectionDB];
                    $actionExecuted = true;
                }

                if (isset($_POST['analizarMazo']) && in_array($_POST['type'], ['basic', 'intermediate'])) {
                    $class = DeckController::class;
                    $method = 'analyze';
                    $params['class'] = [$connectionDB];
                    $params['method'] = [json_decode($_POST['namesCards'], true), intval($_POST['AnaEvo']), $_POST['type']];
                    $actionExecuted = true;
                }

                if (isset($_POST['crearMazo']) && $_POST['level'] == 'basic') {
                    $class = DeckController::class;
                    $method = 'create';
                    $params['class'] = [$connectionDB];
                    $params['method'] = [$res];
                    $actionExecuted = true;
                }

                if (isset($_POST['LemonSqueezy'])) {
                    $class = ProductController::class;
                    $method = "purchaseGems";
                    $params['class'] = [$connectionDB];
                    $params['method'] = [$_POST['LemonSqueezy']];
                    $actionExecuted = true;
                }

                if (isset($_POST['getSectionMembers'])) {
                    $class = SectionController::class;
                    $params['class'] = [$connectionDB];
                    $method = 'show';
                    $params['method'] = [$_POST['getSectionMembers']];
                    $actionExecuted = true;
                }

                if (isset($_POST['showUserBasicData'])) {
                    $class = UserController::class;
                    $method = 'showBasicData';
                    $params['class'] = [$connectionDB, $_SESSION['id']];
                    $actionExecuted = true;
                }
            } else {
                // ... resto de las funciones que no requieren conexión a la base de datos ...
                if (isset($_POST['getDeckBuilderForms'])) {
                    $class = View::class;
                    $params['class'] = ["DeckBuilderFormsView", $_POST];
                    $method = 'render';
                    $actionExecuted = true;
                }

                if (isset($_POST['getSettings'])) {
                    $class = AboutUsController::class;
                    $method = 'getSettings';
                    $actionExecuted = true;
                }

                if (isset($_POST['showCards'])) {
                    $class = CardController::class;
                    $method = 'showAll';
                    $actionExecuted = true;
                }

                if (isset($_POST['infcards'])) {
                    $class = CardController::class;
                    $method = 'infcards';
                    $actionExecuted = true;
                }

                if (isset($_POST['acercaDe'])) {
                    $class = AboutUsController::class;
                    $method = 'showAbout';
                    $actionExecuted = true;
                }

                if (isset($_POST['getSections'])) {
                    $class = SectionController::class;
                    $method = 'show';
                    $params['method'] = [$_POST['getSections']];
                    $actionExecuted = true;
                }

                if (isset($_POST['verCSVersion'])) {
                    $class = AboutUsController::class;
                    $method = 'verCSVersion';
                    $params['method'] = [$res];
                    $actionExecuted = true;
                }

                if (isset($_POST['sobreNosotros'])) {
                    $class = AboutUsController::class;
                    $method = 'showAboutUs';
                    $actionExecuted = true;
                }

                if (isset($_POST['RegLog'])) {
                    if ($_POST['type'] == 'reg') {
                        $class = AboutUsController::class;
                        $method = 'RegLog';
                        $params['method'] = ['SignUp'];
                    } else {
                        $class = AboutUsController::class;
                        $method = 'RegLog';
                        $params['method'] = ['SignIn'];
                    }
                    $actionExecuted = true;
                }

                if (isset($_POST['PreCS'])) {
                    $class = AboutUsController::class;
                    $method = 'showPresentationCs';
                    $actionExecuted = true;
                }

                if (isset($_POST['termcond'])) {
                    $class = AboutUsController::class;
                    $method = 'termcond';
                    $actionExecuted = true;
                }
            }
        }

        if ($TypeAcount == 'google' || $TypeAcount == 'sistem') {
            if (array_intersect(['analizarMazo', 'guardarMazo', 'crearMazo', 'showUserBasicData', 'setTag'], array_keys($_POST))) {

                if (isset($_POST['setTag'])) {
                    $class = UserController::class;
                    $method = 'setTag';
                    $params['class'] = [$connectionDB, $_SESSION['id']];
                    $params['method'] = [$_POST['Tag']];
                    $actionExecuted = true;
                }

                if (isset($_POST['analizarMazo']) && $_POST['type'] == 'advanced') {
                    $class = DeckController::class;
                    $method = 'analyze';
                    $params['class'] = [$connectionDB];
                    $params['method'] = [json_decode($_POST['namesCards'], true), intVal($_POST['AnaEvo']), 'advanced'];
                    $actionExecuted = true;
                }

                if (isset($_POST['guardarMazo'])) {
                    $class = DeckController::class;
                    $method = 'guardarMazo';
                    $params['class'] = [$connectionDB];
                    $params['method'] = [json_decode($_POST['mazo'], true), $_POST['nmazo'], $res];
                    $actionExecuted = true;
                }

                if (isset($_POST['crearMazo']) && ($_POST['level'] == 'intermediate' || $_POST['level'] == 'advanced')) {
                    $class = DeckController::class;
                    $method = 'create';
                    $params['class'] = [$connectionDB];
                    $params['method'] = [$res];
                    $actionExecuted = true;
                }
            }
        }

        // Verificar si no se ha ejecutado ninguna acción y el usuario es invitado
        if (!$actionExecuted && $TypeAcount == 'invitado') {
            $res['state'] = 'error';
            array_push($res['alerts'], '¡Aún eres invitado!, Regístrate y únete a la fiesta de verdad.');
        } else if (!$actionExecuted) {
            $res['state'] = 'error';
            array_push($res['alerts'], 'No se encontró ninguna acción válida para ejecutar.');
        } else if ($actionExecuted) {
            $res['state'] = 'success';
        }
    } catch (\Exception $e) {
        $res['state'] = 'error';
        array_push($res['alerts'], 'Error al procesar la solicitud: ' . $e->getMessage());
    } finally {
        // Liberar la conexión solo si se obtuvo
        if ($connectionDB !== null) {
            $DatabasePool->releaseConnection($connectionDB);
        }
    }

    return array_merge($res, ["class" => $class, "method" => $method, "params" => $params, "actionExecuted" => $actionExecuted]);
}

$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

$router->dispatch($method, $uri);
