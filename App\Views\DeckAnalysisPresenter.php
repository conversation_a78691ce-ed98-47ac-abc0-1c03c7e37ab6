<?php

namespace App\Views;

class DeckAnalysisPresenter
{
    private $rawData;
    private $thresholdsData;
    private $defenseJson;
    private $analysisType;

    public function __construct(array $rawData, array $thresholdsData, object $defenseJson, string $analysisType)
    {
        $this->rawData = $rawData;
        $this->thresholdsData = $thresholdsData;
        $this->defenseJson = $defenseJson;
        $this->analysisType = $analysisType;
    }

    public function getFormattedDataForView(): array
    {
        $viewData = $this->rawData; // Start with raw data structure

        // Format averages (common logic for all approved types)
        if (isset($viewData['averages'])) {
            $viewData['averages'] = $this->formatAverages($viewData['averages']);
        }

        if (isset($viewData['data']['archetype'])) {
            $viewData['data']['formattedArchetype'] = $this->formatArchetype($viewData['data']['archetype']);
        }

        if (isset($viewData['averages']['Versatilidad'])) {
            $viewData['averages']['Versatilidad'] = $this->formatVersatility($viewData['averages']['Versatilidad']);
        }

        if (isset($viewData['averages']['Sinergia'])) {
            $viewData['averages']['Sinergia'] = $this->formatSynergy($viewData['averages']['Sinergia']);
        }

        if (isset($viewData['defenseCoverage'])) {
            $viewData['defenseCoverage'] = $this->formatDefenseCoverage($viewData['defenseCoverage']);
        }
        // Intermediate and Basic types already have the necessary structure from DeckAnalyzer
        // and the common formatAverages handles their display logic.

        // Format weaknesses and suggestions if they exist
        if (isset($viewData['weaknesses'])) {
            $formattedWeaknessesResult = $this->formatWeaknesses($viewData['weaknesses']);
            // Assign the results to the viewData structure
            $viewData['formattedWeaknesses']['generalDescription'] = $formattedWeaknessesResult['generalDescription'];
            $viewData['formattedWeaknesses']['items'] = $formattedWeaknessesResult['items'];
            // Optionally unset the original key if the view only uses the formatted one
            // unset($viewData['weaknesses']);
        }


        return $viewData; // Return the data structured as the view expects
    }

    private function formatAverages(array $averages): array
    {
        // Mensajes detallados explicando cada métrica promedio
        $messagesInfo = [
            'Sinergia' => 'La Sinergia mide qué tan bien interactúan las cartas de tu mazo entre sí. Se calcula analizando combinaciones comunes y efectivas (ej. tanque + soporte), estrategias predefinidas (ej. Montapuercos Ciclo Rápido, Log Bait) y las interacciones específicas entre tus cartas. Un puntaje alto indica que las cartas se complementan bien, permitiendo combos poderosos y una estrategia cohesiva.',
            'Versatilidad' => 'La Versatilidad evalúa la capacidad del mazo para adaptarse a diferentes situaciones y tipos de mazos rivales. Se calcula analizando la variedad de roles cubiertos (ej. daño de área, ataque aéreo, terrestre, hechizos directos, condiciones de victoria) y la presencia de cartas multi-propósito. Un puntaje alto significa que el mazo tiene respuestas para diversas amenazas y puede funcionar en distintas fases del juego.',
            'Ataque' => 'El Ataque representa el potencial ofensivo general del mazo. Se calcula promediando estadísticas clave como el Daño Por Segundo (DPS), Puntos de Vida (HP) y Rango de las cartas consideradas ofensivas (condiciones de victoria, unidades de soporte ofensivo). Un puntaje alto sugiere que el mazo puede ejercer una fuerte presión y destruir torres rápidamente.',
            'Defensa' => 'La Defensa mide la capacidad del mazo para proteger tus torres y contrarrestar los ataques enemigos. Se calcula promediando estadísticas como DPS, HP y Rango de las cartas consideradas defensivas (estructuras defensivas, unidades con buen DPS/HP para defender). Un puntaje alto indica que el mazo es sólido en defensa y puede resistir diferentes tipos de asaltos.',
        ];

        foreach ($averages as $label => &$section) {
            // Ensure totalScore exists, default to 0 if not (might happen in edge cases)
            $score = $section['totalScore'] ?? 0;
            $section['displayScore'] = $score; // Keep original score
            $section['displayMessage'] = 'N/A'; // Default message
            $section['displayClass'] = ''; // Default CSS class
            $section['msgInfo'] = $messagesInfo[$label] ?? 'Información no disponible.'; // Info message

            if (isset($this->thresholdsData['thresholds'][$label])) {
                // Sort thresholds descending to find the correct one
                krsort($this->thresholdsData['thresholds'][$label]);
                foreach ($this->thresholdsData['thresholds'][$label] as $threshold => $message) {
                    if ($score >= $threshold) {
                        $section['displayMessage'] = $message;
                        // Find the correct CSS class
                        krsort($this->thresholdsData['levelClasses']); // Sort classes descending
                        foreach ($this->thresholdsData['levelClasses'] as $classThreshold => $class) {
                            if ($score >= $classThreshold) {
                                $section['displayClass'] = $class;
                                break; // Exit class loop once found
                            }
                        }
                        break; // Exit threshold loop once found
                    }
                }
            }
            // Process additional messages if they exist
            if (!empty($section['msg']) && is_array($section['msg'])) {
                $section['formattedMessages'] = implode('<br><br>', array_map('htmlspecialchars', $section['msg']));
            } else {
                $section['formattedMessages'] = '';
            }
        }
        unset($section); // Break reference
        return $averages;
    }

    private function formatVersatility(array $versatilityData): array
    {
        // Detailed explanation for Versatility
        $generalDescription = "La Versatilidad mide la capacidad del mazo para manejar diversas situaciones ofensivas y defensivas. Se analiza la distribución de cartas en roles clave: Condiciones de Victoria (win), unidades Terrestres (ter), unidades Aéreas (aer), unidades de Enjambre/Defensa (dew), y Hechizos (hech). Un mazo versátil tiene una buena mezcla de estos roles, permitiéndole adaptarse a diferentes estrategias enemigas y fases del juego. Se calcula evaluando la cobertura de estos roles y la efectividad de las combinaciones de hechizos disponibles.";
        $versatilityData['generalDescription'] = $generalDescription; // Add description to data

        // Combine spells for easier display
        $versatilityData['cardhech'] = array_merge(
            $versatilityData['cardhech']['hech0'] ?? [],
            $versatilityData['cardhech']['hech1'] ?? [],
            $versatilityData['cardhech']['hech2'] ?? [],
            $versatilityData['cardhech']['hech3'] ?? [],
            $versatilityData['cardhech']['hech4'] ?? []
        );

        // Generate image HTML
        $cardTypes = ['win', 'ter', 'aer', 'dew', 'hech'];
        $imgArrays = [];
        foreach ($cardTypes as $type) {
            $imgArrays[$type] = array_map(function ($card): string {
                $iconUrl = is_object($card) && isset($card->urlIcon) ? htmlspecialchars($card->urlIcon) : './static/media/styles/icons/icon_card_denegado.webp';
                return "<div class='cs-card'><img class='cs-card__image' src='$iconUrl' alt='card_image'></div>";
            }, $versatilityData["card{$type}"] ?? []);
        }
        $versatilityData['imgArrays'] = $imgArrays; // Add image HTML

        return $versatilityData;
    }

    private function formatSynergy(array $synergyData): array
    {
        // Detailed explanation for Synergy
        $generalDescription = "La Sinergia evalúa qué tan bien funcionan juntas las cartas de tu mazo. Se analiza de dos maneras: 1) Sinergia Estratégica: Compara tu mazo con arquetipos y estrategias conocidas (ej. Log Bait, Pekka Bridge Spam), asignando puntos si tu mazo sigue patrones efectivos. 2) Sinergia de Cartas: Examina las interacciones directas entre pares de cartas en tu mazo (ej. Tanque + Soporte Aéreo, Minero + Pandilla de Duendes), otorgando puntos por combinaciones que se potencian mutuamente. Un puntaje alto indica un mazo cohesivo con combos efectivos.";
        $synergyData['generalDescription'] = $generalDescription; // Add description to data

        // Format strategy points and reasons
        if (!empty($synergyData['arraySynergyStrategy'])) {
            foreach ($synergyData['arraySynergyStrategy'] as &$strategy) {
                $strategy['stylePointsStrategiCard'] = match (true) {
                    $strategy['pointsSynergy'] > 10 => 'color: var(--cs-color-VibrantTurquoise);',
                    $strategy['pointsSynergy'] > 5 => 'color: var(--cs-color-GoldenYellow);',
                    $strategy['pointsSynergy'] >= 0 => 'color: var(--cs-color-IntenseOrange);',
                    default => ''
                };
                // Generate reasonsOutput HTML using <ul><li> structure for strategies
                $reasonsOutput = '<ul class="cs-list cs-list--small">';
                if (!empty($strategy['reasons'])) {
                    foreach ($strategy['reasons'] as $reason) {
                        $pointsText = ($reason['points'] >= 0 ? '+' : '') . htmlspecialchars($reason['points']) . ' pts';
                        $reasonsOutput .= sprintf('<li>%s: %s</li>', htmlspecialchars($reason['reason']), $pointsText);
                    }
                } else {
                    $reasonsOutput .= '<li>No hay detalles específicos.</li>';
                }
                $reasonsOutput .= '</ul>';
                $strategy['reasonsOutput'] = $reasonsOutput;
            }
            unset($strategy); // Break reference
        }

        // Format card synergy points and reasons
        if (!empty($synergyData['arraySynergyCards'])) {
            foreach ($synergyData['arraySynergyCards'] as &$synergyCard) {
                $synergyCard['stylePointsStrategyGeneral'] = match (true) {
                    $synergyCard['pointsSynergy'] > 10 => 'color: var(--cs-color-VibrantTurquoise);',
                    $synergyCard['pointsSynergy'] > 5 => 'color: var(--cs-color-GoldenYellow);',
                    $synergyCard['pointsSynergy'] >= 0 => 'color: var(--cs-color-IntenseOrange);',
                    default => ''
                };
                if (!empty($synergyCard['SynergyCards'])) {
                    foreach ($synergyCard['SynergyCards'] as &$synergyCardSynergy) {
                        // Generate reasonsOutput HTML using <ul><li> structure for card interactions
                        $reasonsOutput = '<ul class="cs-list cs-list--small">';
                        if ($synergyCardSynergy['pointsSynergy'] > 0 && !empty($synergyCardSynergy['reasons'])) {
                            foreach ($synergyCardSynergy['reasons'] as $reason) {
                                $pointsText = ($reason['points'] >= 0 ? '+' : '') . htmlspecialchars($reason['points']) . ' pts';
                                $reasonsOutput .= sprintf('<li>%s: %s</li>', htmlspecialchars($reason['reason']), $pointsText);
                            }
                        } elseif ($synergyCardSynergy['pointsSynergy'] <= 0) {
                            $reasonsOutput .= '<li>Interacción neutra o negativa no detallada.</li>'; // Handle non-positive synergy if needed
                        } else {
                            $reasonsOutput .= '<li>No hay detalles específicos.</li>';
                        }
                        $reasonsOutput .= '</ul>';
                        $synergyCardSynergy['reasonsOutput'] = $reasonsOutput;
                    }
                    unset($synergyCardSynergy); // Break reference
                }
            }
            unset($synergyCard); // Break reference
        }

        return $synergyData;
    }

    private function formatDefenseCoverage(array $defenseCoverage): array
    {
        $formattedCoverage = [];
        // General description added here, assuming it might be useful in the view later
        $generalDescription = "La Cobertura Defensiva analiza si tu mazo posee respuestas adecuadas contra diferentes tipos de amenazas comunes. Se evalúa la presencia de cartas capaces de contrarrestar eficazmente cada categoría (ej. daño de área para enjambres, unidades aéreas para tanques aéreos). Un 'Sí' indica que tienes al menos una respuesta viable, mientras que un 'No' sugiere una posible vulnerabilidad.";

        // Store the general description if needed by the view
        $formattedCoverage['generalDescription'] = $generalDescription; // Uncomment if needed

        foreach ($defenseCoverage as $type => $coverageInfo) {
            // Assuming $coverageInfo might be an array like ['isCovered' => bool, 'cards' => array]
            // If $coverageInfo is just a boolean, adjust accordingly.
            $isCovered = is_array($coverageInfo) ? ($coverageInfo['isCovered'] ?? false) : $coverageInfo;
            $contributingCards = is_array($coverageInfo) ? ($coverageInfo['cards'] ?? []) : []; // Array of card names/objects contributing

            $baseDescription = $this->defenseJson->descriptions->{$type} ?? 'Descripción no disponible.';
            // Explanation of how it's calculated
            $howItWorks = " Se determina verificando si el mazo incluye cartas efectivas contra este tipo de amenaza (según roles y estadísticas predefinidas).";
            // Show contributing cards if available and covered
            $cardExamples = $isCovered && !empty($contributingCards)
                ? " Cartas en tu mazo que contribuyen: " . htmlspecialchars(implode(', ', $contributingCards)) . "."
                : ($isCovered ? " Se detectaron cartas genéricas que aportan cobertura." : " No se detectaron cartas específicas en tu mazo para esta cobertura.");
            // Meaning of the result
            $meaning = $isCovered ? " Tu mazo parece tener cobertura contra esta amenaza." : " Tu mazo podría ser vulnerable a esta amenaza.";

            $formattedCoverage['items'][$type] = [
                'isCovered' => $isCovered,
                'displayName' => $this->defenseJson->names->{$type} ?? ucfirst(str_replace('_', ' ', $type)),
                // Combine detailed info for the description/tooltip
                'description' => htmlspecialchars($baseDescription . $howItWorks . $meaning . $cardExamples)
            ];
        }
        return $formattedCoverage; // Return only the formatted types
    }

    private function formatArchetype(array $archetypeInfo): string
    {
        $archetype = htmlspecialchars($archetypeInfo['archetype']);
        $details = $archetypeInfo['details'] ?? [];

        if ($archetypeInfo['archetype'] === 'Híbrido' && !empty($details)) {
            $this->analysisType != 'basic' ?
                $output = '<span class="cs-color-GoldenYellow">Híbrido</span> <span class="text-sm cs-color-GoldenYellow">(Coincide parcialmente con: ' . htmlspecialchars(implode(', ', $details)) . ')</span>' :
                $output = '<span class="acs-color-GoldenYellow">Híbrido</span>';
        } elseif ($archetypeInfo['archetype'] === 'Desconocido') {
            $output = '<span class="cs-color-IntenseOrange">Desconocido</span>';
        } else {
            $output = '<span class="cs-color-GoldenYellow">' . $archetype . '</span>';
        }

        // Base description explaining the calculation and purpose
        $baseDescription = "El arquetipo identifica el estilo de juego principal de tu mazo. Se determina comparando la composición de tu mazo (cartas clave, coste promedio, coste de ciclo) con patrones conocidos (ej. Log Bait, Lavaloon, Pekka Bridge Spam). Verificamos: 1) Presencia de cartas esenciales para el arquetipo. 2) Coste de elixir promedio y de ciclo dentro de los rangos típicos. 3) Ausencia de cartas que contradigan el estilo del arquetipo. Conocer tu arquetipo te ayuda a entender sus fortalezas, debilidades y plan de juego general.";

        // Specific description based on the identified archetype
        $specificDescription = '';
        if ($archetypeInfo['archetype'] === 'Híbrido' && !empty($details)) {
            $specificDescription = 'Este mazo se clasifica como Híbrido porque comparte características significativas con varios arquetipos: ' . htmlspecialchars(implode(', ', $details)) . '. Esto puede ofrecer flexibilidad, pero a veces carece de la especialización de un arquetipo puro. ';
        } elseif ($archetypeInfo['archetype'] === 'Desconocido') {
            $specificDescription = 'Este mazo no encaja claramente en ninguno de los arquetipos estándar definidos. Podría ser una estrategia innovadora, una combinación menos común, o requerir ajustes para definirse mejor. ';
        } else {
            // For known archetypes
            $specificDescription = 'Este mazo se identifica claramente como ' . $archetype . '. ';
        }

        $finalDescription = $specificDescription . $baseDescription;

        // Generate tooltip HTML with increased width for better readability
        $tooltipHtml = ' <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="info" data-width="20rem" data-inf="' . htmlspecialchars($finalDescription) . '">';

        return $output . $tooltipHtml;
    }

    /**
     * Formats the weaknesses and suggestions array for the view, adding a general description.
     *
     * @param array $weaknessesData Array containing 'weaknesses' list and potentially other info.
     * @return array An array containing 'generalDescription' and 'items' (list of weaknesses).
     */
    private function formatWeaknesses(array $weaknessesData): array
    {
        $formattedItems = [];
        // General description explaining how weaknesses are identified and the purpose of suggestions
        $generalDescription = "Las debilidades identifican áreas donde tu mazo podría tener problemas o carencias significativas. Se detectan analizando factores como la falta de cobertura defensiva contra ciertos tipos de ataque (ej. aéreo pesado, enjambres terrestres), un coste de elixir muy alto o bajo que dificulta la gestión, baja sinergia entre cartas clave, o la ausencia de una condición de victoria clara y fiable. Las sugerencias proporcionadas buscan mitigar estas debilidades, proponiendo cambios de cartas específicos o ajustes generales en la estrategia para mejorar el rendimiento y la consistencia del mazo.";

        // Check if the 'weaknesses' key exists and is an array
        if (!empty($weaknessesData['weaknesses']) && is_array($weaknessesData['weaknesses'])) {
            foreach ($weaknessesData['weaknesses'] as $weaknessName => $suggestion) {
                // Ensure suggestion is a string before escaping
                $suggestionText = is_string($suggestion) ? $suggestion : 'Sugerencia no disponible.';
                $formattedItems[] = [
                    'name' => htmlspecialchars($weaknessName),
                    'suggestion' => htmlspecialchars($suggestionText)
                ];
            }
        }

        // Return the structured array
        return [
            'generalDescription' => $generalDescription,
            'items' => $formattedItems
        ];
    }
}
