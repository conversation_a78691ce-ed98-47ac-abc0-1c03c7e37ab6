<div class="cs-analysis cs-analysis--default">
    <?php if ($data['type'] == 'intermediate' || $data['type'] == 'advanced') { ?>
        <h2>Resultados del análisis</h2>
        <div class="alert alert--info">
            <span class="alert__message">Esta herramienta está en fase de desarrollo.
                Los resultados deben ser considerados una guía y no una evaluación definitiva.</span>
        </div>
    <?php } ?>

    <!-- Sección: Vista rápida -->
    <div class="cs-analysis__section cs-analysis__section--default">
        <h3>Vista rápida</h3>
        <?php foreach ($data['averages'] as $label => $section) { ?>
            <span><span><?php echo htmlspecialchars($label); ?></span> :
                <span class="<?php echo htmlspecialchars($section['displayClass']); ?>">
                    <?php echo htmlspecialchars($section['displayScore']); ?>%
                    <?php echo htmlspecialchars($section['displayMessage']); ?>
                </span>
                <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="msgInfo"
                    data-width="12rem" data-inf="<?php echo htmlspecialchars($section['msgInfo']); ?>">
            </span><br>
        <?php } ?>
        <div id="div_view_quik_aditional">
            <span class="cs-color-LightGrey">Arquetipo: </span><?php echo $data['data']['formattedArchetype']; ?>
        </div>
        <div id="div_data_view_quick">
            <img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/card_stat_inf/icon_gota_elixir.webp"
                alt="cycle">
            <span class="color-elixir"><?php echo htmlspecialchars($data['data']['averageElixirCost']); ?></span>
            &nbsp;&nbsp;
            <img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/icon_cycle.webp"
                alt="shortCycle">&nbsp;
            <span class="color-elixir"><?php echo htmlspecialchars($data['data']['shortCycle']); ?></span>&nbsp;
        </div>
    </div>

    <?php if ($data['type'] == 'intermediate' || $data['type'] == 'advanced') { ?>
        <h2>Detalles</h2>
        <?php if (isset($data['defenseCoverage']) && is_array($data['defenseCoverage']) && !empty($data['defenseCoverage'])) { ?>
            <!-- Sección: Cobertura de Defensa -->
            <div class="cs-analysis__section cs-analysis__section--default">
                <h3>Cobertura de Defensa<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                        alt="msgInfo" data-width="12rem"
                        data-inf="<?php echo htmlspecialchars($data['defenseCoverage']['generalDescription']); ?>"></h3>
                <table class="cs-table cs-table--dark">
                    <thead>
                        <tr>
                            <th>Tipo de Defensa</th>
                            <th>Cobertura</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data['defenseCoverage']['items'] as $type => $coverageInfo) { ?>
                            <tr>
                                <td>
                                    <?php echo htmlspecialchars($coverageInfo['displayName']); ?>
                                    <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="Info"
                                        data-inf="<?php echo htmlspecialchars($coverageInfo['description']); ?>">
                                </td>
                                <td><?php echo $coverageInfo['isCovered'] ? '✅' : '❌'; ?></td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        <?php } ?>

        <!-- Sección: Posibles Debilidades -->
        <?php // Updated condition to check for the new formatted data key ?>
        <?php if (isset($data['formattedWeaknesses']) && !empty($data['formattedWeaknesses'])) { ?>
            <div class="cs-analysis__section cs-analysis__section--default">
                <h3>Posibles Debilidades y Sugerencias<img class="cs-tooltip-image"
                        src="./static/media/styles/icons/info-circle.svg" alt="msgInfo" data-width="12rem"
                        data-inf="<?php echo htmlspecialchars($data['formattedWeaknesses']['generalDescription']); ?>"></h3>
                <?php // Updated loop to iterate over the formatted weaknesses array ?>
                <?php foreach ($data['formattedWeaknesses']['items'] as $weaknessItem) { ?>
                    <div class="m-1">
                        <div class="cs-color-IntenseOrange text-center"> <!-- Weakness Name -->
                            <?php echo $weaknessItem['name']; // Already escaped in Presenter ?>
                        </div>
                        <div class="cs-color-GoldenYellow text-center"> <!-- Suggestion Text -->
                            <?php echo $weaknessItem['suggestion']; // Already escaped in Presenter ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php } ?>

        <?php foreach ($data['averages'] as $keySection => $section) {
            if ($keySection == 'Versatilidad' && isset($section['imgArrays'])) {
                $imgArrays = $section['imgArrays']; ?>
                <!-- Sección: Detalles de ataque/defensa -->
                <div class="cs-analysis__section cs-analysis__section--default">
                    <h3>Versatilidad - Ataque y Defensa<img class="cs-tooltip-image"
                            src="./static/media/styles/icons/info-circle.svg" alt="msgInfo" data-width="12rem"
                            data-inf="<?php echo htmlspecialchars($section['generalDescription']); ?>"></thead>
                    </h3>
                    <table class="cs-table cs-table--dark">
                        <thead>
                            <tr>
                                <th></th>
                                <th>Condición de Victoria</th>
                                <th>Terrestre</th>
                                <th>Aéreo</th>
                                <th>Defensa Clave</th>
                                <th>Hechizos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <th>Cartas</th>
                                <td><?php echo implode(" ", $imgArrays['win'] ?? []) ?></td>
                                <td><?php echo implode(" ", $imgArrays['ter'] ?? []) ?></td>
                                <td><?php echo implode(" ", $imgArrays['aer'] ?? []) ?></td>
                                <td><?php echo implode(" ", $imgArrays['dew'] ?? []) ?></td>
                                <td><?php echo implode(" ", $imgArrays['hech'] ?? []) ?></td>
                            </tr>
                            <tr>
                                <th>Ataque</th>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardwin']['ataque'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardter']['ataque'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardaer']['ataque'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['carddew']['ataque'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardhech']['ataque'] ?? 'N/A'); ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Defensa</th>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardwin']['defensa'] ?? 'N/A'); ?>
                                </td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardter']['defensa'] ?? 'N/A'); ?>
                                </td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardaer']['defensa'] ?? 'N/A'); ?>
                                </td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['carddew']['defensa'] ?? 'N/A'); ?>
                                </td>
                                <td><?php echo htmlspecialchars($section['averagesPerGroup']['cardhech']['defensa'] ?? 'N/A'); ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <?php }
            if ($keySection == 'Sinergia' && isset($section['arraySynergyStrategy'])) { ?>
                <!-- Sección: Detalles de sinergia -->
                <div class="cs-analysis__section cs-analysis__section--default">
                    <h3>Sinergias<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="msgInfo"
                            data-width="12rem" data-inf="<?php echo htmlspecialchars($section['generalDescription']); ?>"></h3>
                    <!-- Sección: Top Sinergias Positivas -->
                    <?php if (!empty($section['topPositiveSynergies'])) { ?>
                        <div class="cs-analysis__sub-section cs-analysis__section--default">
                            <h4 class="cs-color-GoldenYellow">Sinergias Clave Positivas</h4>
                            <ul>
                                <?php foreach ($section['topPositiveSynergies'] as $synergy) { ?>
                                    <li>
                                        <span><?php echo htmlspecialchars($synergy['context']); ?>:</span>
                                        <span
                                            class="cs-color-VibrantTurquoise text-center">+<?php echo number_format($synergy['score'], 2); ?>
                                            pts</span>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    <?php } ?>

                    <!-- Sección: Top Sinergias Negativas -->
                    <?php if (!empty($section['topNegativeSynergies'])) { ?>
                        <div class="cs-analysis__sub-section cs-analysis__section--default">
                            <h4 class="cs-color-GoldenYellow">Puntos Débiles de Sinergia</h4>
                            <ul>
                                <?php foreach ($section['topNegativeSynergies'] as $synergy) { ?>
                                    <li>
                                        <span><?php echo htmlspecialchars($synergy['context']); ?>:</span>
                                        <span class="cs-color-IntenseOrange text-center"><?php echo number_format($synergy['score'], 2); ?>
                                            pts</span>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    <?php } ?>

                    <div class="cs-analysis__sub-section cs-analysis__section--default cards__details">
                        <p class="cs-color-GoldenYellow">Estrategia General:
                            <span class="cs-color-LightGrey">
                                <?php echo htmlspecialchars($section['totalPointsSynergyStrategy'] ?? 0); ?> Puntos
                            </span>
                            <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="inf"
                                data-width="12rem"
                                data-inf="<?php echo htmlspecialchars($section['generalDescription'] ?? 'Información no disponible.'); ?>">
                        </p>
                        <div class="cs-deck cs-deck--default">
                            <?php foreach ($section['arraySynergyStrategy'] as $strategy) { ?>
                                <div class="cs-card cs-card--medium">
                                    <img class="cs-card__image" src="<?php echo htmlspecialchars($strategy['medium']); ?>"
                                        alt='card_img'>
                                    <span class="span_infver"
                                        style="<?php echo htmlspecialchars($strategy['stylePointsStrategiCard'] ?? ''); ?>">+
                                        <?php echo htmlspecialchars($strategy['pointsSynergy']); ?>pts
                                    </span>
                                    <img class="cs-analysis-sinergy-cards__toltip cs-tooltip-image"
                                        src="./static/media/styles/icons/info-circle.svg" alt="inf" data-width="15em"
                                        data-overflow="true" data-inf='<?php echo $strategy['reasonsOutput'] ?? 'No hay detalles.'; ?>'>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="cs-analysis__sub-section cs-analysis__section--default">
                        <p class="cs-color-GoldenYellow">Sinergias de Cartas:
                            <span><?php echo htmlspecialchars($section['totalPointsSynergyCards'] ?? 0); ?> Puntos</span>
                            <img class=" cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="inf"
                                data-width="12rem"
                                data-inf="<?php echo htmlspecialchars($section['generalDescription'] ?? 'Información no disponible.'); ?>">
                        </p>
                        <?php foreach ($section['arraySynergyCards'] as $synergyCard) { ?>
                            <div class="cs-analysis-sinergy-cards">
                                <div class="cs-analysis-sinergy-cards__card">
                                    <div class="cs-card">
                                        <span class="span_infver"
                                            style="<?php echo htmlspecialchars($synergyCard['stylePointsStrategyGeneral'] ?? ''); ?>">+
                                            <?php echo htmlspecialchars($synergyCard['pointsSynergy'] ?? 0) . 'pts'; ?>
                                        </span>
                                        <img class='cs-card__image' src="<?php echo htmlspecialchars($synergyCard['medium']); ?>"
                                            alt='img_aumento'>
                                    </div>
                                    <img class="cs-analysis-sinergy-cards__img_aumento"
                                        src="./static/media/styles/icons/icon_aumento.webp" alt="aumento">
                                </div>
                                <div class="cs-analysis-sinergy-cards__cards">
                                    <?php if (($synergyCard['pointsSynergy'] ?? 0) <= 0) { ?>
                                        <div class="cs-card">
                                            <img class="card__not-found" src="./static/media/styles/icons/icon_card_denegado.webp">
                                        </div>
                                    <?php } else {
                                        foreach ($synergyCard['SynergyCards'] as $synergyCardSynergy) {
                                            if (($synergyCardSynergy['pointsSynergy'] ?? 0) > 0) { ?>
                                                <div class="cs-card">
                                                    <img class='cs-card__image' src="<?php echo htmlspecialchars($synergyCardSynergy['medium']); ?>"
                                                        alt='medium'>
                                                    <span class="span_infver">+
                                                        <?php echo htmlspecialchars($synergyCardSynergy['pointsSynergy']); ?>pts</span>
                                                    <img class="cs-analysis-sinergy-cards__toltip cs-tooltip-image"
                                                        src="./static/media/styles/icons/info-circle.svg" alt="inf" data-width="15em"
                                                        data-overflow="true"
                                                        data-inf='<?php echo $synergyCardSynergy['reasonsOutput'] ?? 'No hay detalles.'; ?>'>
                                                </div>
                                            <?php }
                                        }
                                    } ?>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php }
            // Mostrar mensajes adicionales formateados
            if (!empty($section['formattedMessages'])) { ?>
                <br>
                <hr>
                <h3 class="cs-color-GoldenYellow">Notas Adicionales</h3>
                <?php echo htmlspecialchars_decode($section['formattedMessages']);
            }
        } ?>

        <script>
            Config.addSlick("img", $('.cs-analysis-sinergy-cards__cards'), 3);
        </script>
    <?php } ?>
</div>