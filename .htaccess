<IfModule mod_rewrite.c>
    RewriteEngine On

    # Verificar que no estamos en localhost
    RewriteCond %{REMOTE_ADDR} !^(127\.0\.0\.1|::1)$
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Bloquea el acceso directo a directorios sensibles desde el cliente
    RewriteCond %{REQUEST_URI} ^/(App|Config|Data|Services|Controllers|Models|Views|tools|deckanalyzer|deckbuilder)/
    RewriteRule .* - [F,L]

    # Reescribe las URLs sin extensión a .php solo si el archivo .php existe y no es un directorio
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}\.php -f
    RewriteRule ^(.+?)/?$ $1.php [L]

    # Modo Mantenimiento
    #RewriteCond %{REQUEST_URI} !^/maintenance\.php$
    #RewriteCond %{REMOTE_ADDR} !^192\.168\.1\.1$
    #RewriteRule ^(.*)$ /maintenance.php [R=307,L]
</IfModule>
